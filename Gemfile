# source 'https://mirrors.aliyun.com/rubygems/'
source 'https://gems.ruby-china.com'
# source 'https://rubygems.org'
# A Ruby gem to load environment variables from `.env`.
# https://github.com/bkeepers/dotenv
# If you use gems that require environment variables to be set before they are loaded, then list dotenv-rails in the Gemfile before those other gems and require dotenv/rails-now.
gem 'dotenv-rails', require: 'dotenv/load'
# git_source(:tallty) { |repo| "******************:#{repo}.git" }
git_source(:tallty) { |repo| "https://tallty_deploy:<EMAIL>/#{repo}.git" }

git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.2.2'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails', '~> 7.1'
# Use sqlite3 as the database for Active Record
# gem 'sqlite3', '~> 1.4'
# Use Puma as the app server
gem 'puma', '~>6'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
# gem 'jbuilder', '~> 2.7'
# Use Redis adapter to run Action Cable in production
gem 'redis', '~> 4.0'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'
gem 'nokogiri'
gem 'sidekiq', '~>6.4'
gem 'sidekiq-scheduler'
# Use Active Storage variant
# gem 'image_processing', '~> 1.2'
gem 'mysql2'
# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.4', require: false

# KingbaseSync gem for real-time data synchronization
gem 'kingbase_sync', git: 'https://oauth2:<EMAIL>/clousky/kingbase_sync.git'

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem 'rack-cors'

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
end

group :development do
  gem 'listen', '~> 3.3'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]
# Use PostgresSql as the database for deploy
gem 'pg'
# This is a library generating unique id in short pattern. https://rubygems.org/gems/uuid64
# https://github.com/heckpsi-lab/uuid64
gem 'uuid64'
# A set of Rails responders to dry up your application http://blog.plataformatec.com.br/
# https://github.com/plataformatec/responders
# rails g responders:install
gem 'acts_as_pasting', tallty: 'open-source/acts_as_pasting'
gem 'mina', require: false
gem 'mina-multistage', require: false
gem 'pry-byebug'
gem 'responders'
gem 'rubocop', require: false
gem 'rubocop-performance'
gem 'rubocop-rails'
gem 'simple_controller', tallty: 'open-source/simple_controller'
gem 'tallty_import_export', tallty: 'open-source/tallty_import_export'
# 爬虫相关
gem 'mini_magick'
gem 'neighbor'
gem 'ruby-openai'
gem 'selenium-webdriver'
gem 'tf-idf-similarity'
gem 'webdrivers'

gem 'rails_action_core', tallty: 'ta-rails/rails_action_core'
gem 'rails_assessment', tallty: 'ta-rails/rails_assessment'
gem 'rails_audit', tallty: 'ta-rails/rails_audit'
gem 'rails_bot', tallty: 'ta-llm/rails_bot'
gem 'rails_bpm', tallty: 'ta-rails/rails_bpm'
gem 'rails_chat', tallty: 'ta-rails/rails_chat'
gem 'rails_com', tallty: 'ta-rails/rails_com'
gem 'rails_data', tallty: 'ta-rails/rails_data'
gem 'rails_dingtalk', tallty: 'ta-rails/rails_dingtalk'
gem 'rails_discount', tallty: 'ta-rails/rails_discount'
gem 'rails_discuss', tallty: 'ta-rails/rails_discuss'
gem 'rails_disk', tallty: 'ta-rails/rails_disk'
gem 'rails_geo', tallty: 'ta-rails/rails_geo'
gem 'rails_grant', tallty: 'ta-rails/rails_grant'
gem 'rails_notice', tallty: 'ta-rails/rails_notice'
gem 'rails_notify', tallty: 'ta-rails/rails_notify'
gem 'rails_oauth', tallty: 'ta-rails/rails_oauth'
gem 'rails_office', tallty: 'ta-rails/rails_office'
gem 'rails_opm', tallty: 'ta-rails/rails_opm', branch: 'leave'
gem 'rails_perf', tallty: 'ta-rails/rails_perf'
gem 'rails_permit', tallty: 'ta-rails/rails_permit'
gem 'rails_plan', tallty: 'ta-rails/rails_plan'
gem 'rails_pms', tallty: 'ta-rails/rails_pms'
gem 'rails_reg', tallty: 'ta-rails/rails_reg'
gem 'rails_res', tallty: 'ta-rails/rails_res'
gem 'rails_rss', tallty: 'ta-rails/rails_rss'
gem 'rails_scoretee', tallty: 'ta-rails/rails_scoretee'
gem 'rails_serve', tallty: 'ta-rails/rails_serve'
gem 'rails_sms_auth', tallty: 'ta-rails/rails_sms_auth'
gem 'rails_soa_auth', tallty: 'ta-rails/rails_soa_auth'
gem 'rails_soa_file', tallty: 'ta-rails/rails_soa_file'
gem 'rails_state', tallty: 'ta-rails/rails_state'
gem 'rails_svr', tallty: 'ta-rails/rails_svr'
gem 'rails_tofu', tallty: 'ta-rails/rails_tofu'
gem 'rails_tour', tallty: 'ta-rails/rails_tour'
gem 'rails_trans', tallty: 'ta-rails/rails_trans'

group :development, :test do
  gem 'factory_bot_rails'
  gem 'rspec-rails'
  gem 'shoulda-matchers'
  # Generate Swagger 2.0 docs for Rails apps using RSpec request specs. Test results can be captured as response examples.
  # https://github.com/drewish/rspec-rails-swagger
  # rails generate rspec:swagger_install
  # rails generate rspec:swagger PostsController
  # bundle exec rake swagger
  gem 'annotate'
  gem 'pry-rails'
  gem 'rspec-rails-swagger', tallty: 'open-source/rspec-rails-swagger'
  gem 'simplecov', require: false
end
